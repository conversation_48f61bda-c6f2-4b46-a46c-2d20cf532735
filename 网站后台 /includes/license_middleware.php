<?php
/**
 * 卡密验证中间件
 * 统一的卡密验证和到期检查机制
 */

class LicenseMiddleware {
    private $pdo;
    private $strict_mode;
    
    public function __construct($pdo, $strict_mode = true) {
        $this->pdo = $pdo;
        $this->strict_mode = $strict_mode;
    }
    
    /**
     * 验证卡密是否有效
     * @param string $license_key 卡密
     * @param array $required_permissions 需要的权限 ['customer_service', 'product_listing']
     * @param bool $silent_mode 静默模式，不记录错误日志
     * @return array 验证结果
     */
    public function validateLicense($license_key, $required_permissions = [], $silent_mode = false) {
        try {
            // 检查卡密格式
            if (empty($license_key) || strlen($license_key) > 255) {
                if (!$silent_mode) {
                    $this->logSecurityEvent($license_key, 'INVALID_FORMAT');
                }
                return $this->createErrorResponse('访问被拒绝', 'INVALID_FORMAT');
            }

            // 【优化】查询卡密信息 - 使用UNIQUE索引快速查找
            $stmt = $this->pdo->prepare("
                SELECT id, key_value, status, expiry_date, expires_at,
                       has_customer_service, has_product_listing, type
                FROM license_keys
                WHERE key_value = ?
                LIMIT 1
            ");
            $stmt->execute([$license_key]);
            $license = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$license) {
                if (!$silent_mode) {
                    $this->logSecurityEvent($license_key, 'KEY_NOT_FOUND');
                }
                return $this->createErrorResponse('卡密不存在，请检查卡密是否正确', 'KEY_NOT_FOUND');
            }

            // 检查卡密状态
            if ($license['status'] !== 'active') {
                if (!$silent_mode) {
                    $this->logSecurityEvent($license_key, 'KEY_DISABLED');
                }

                // 【修复】根据具体状态返回不同的错误消息和错误代码
                $error_message = $this->getStatusMessage($license['status']);
                $error_code = $this->getStatusErrorCode($license['status']);

                return $this->createErrorResponse($error_message, $error_code);
            }

            // 检查过期时间
            $expiry_check = $this->checkExpiry($license);
            if (!$expiry_check['valid']) {
                // 自动更新过期卡密状态
                $this->updateExpiredLicense($license['id']);
                if (!$silent_mode) {
                    $this->logSecurityEvent($license_key, 'KEY_EXPIRED');
                }
                return $this->createErrorResponse('访问被拒绝', 'KEY_EXPIRED');
            }

            // 检查权限
            if (!empty($required_permissions)) {
                $permission_check = $this->checkPermissions($license, $required_permissions);
                if (!$permission_check['valid']) {
                    if (!$silent_mode) {
                        $this->logSecurityEvent($license_key, 'INSUFFICIENT_PERMISSIONS');
                    }
                    return $this->createErrorResponse('访问被拒绝', 'INSUFFICIENT_PERMISSIONS');
                }
            }

            // 更新最后使用时间
            $this->updateLastUsed($license['id']);

            return $this->createSuccessResponse($license);

        } catch (Exception $e) {
            if (!$silent_mode) {
                error_log("卡密验证异常: " . $e->getMessage());
            }
            return $this->createErrorResponse('访问被拒绝', 'SYSTEM_ERROR');
        }
    }
    
    /**
     * 检查卡密是否过期
     */
    private function checkExpiry($license) {
        $expiry_field = null;
        $expiry_date = null;
        
        // 兼容多种过期时间字段
        if (!empty($license['expiry_date'])) {
            $expiry_field = 'expiry_date';
            $expiry_date = new DateTime($license['expiry_date']);
        } elseif (!empty($license['expires_at'])) {
            $expiry_field = 'expires_at';
            $expiry_date = new DateTime($license['expires_at']);
        }
        
        if ($expiry_date) {
            $now = new DateTime();
            if ($now > $expiry_date) {
                return [
                    'valid' => false,
                    'message' => '卡密已过期，请联系代理商续费'
                ];
            }
            
            // 检查即将过期（7天内）
            $diff = $expiry_date->getTimestamp() - $now->getTimestamp();
            $days_left = floor($diff / (24 * 60 * 60));
            
            if ($days_left <= 7 && $days_left > 0) {
                return [
                    'valid' => true,
                    'warning' => "卡密将在 {$days_left} 天后过期，请及时续费"
                ];
            }
        }
        
        return ['valid' => true];
    }
    
    /**
     * 检查卡密权限
     */
    private function checkPermissions($license, $required_permissions) {
        $has_customer_service = $license['has_customer_service'] ?? 0;
        $has_product_listing = $license['has_product_listing'] ?? 0;
        
        foreach ($required_permissions as $permission) {
            switch ($permission) {
                case 'customer_service':
                    if (!$has_customer_service) {
                        return [
                            'valid' => false,
                            'message' => '此卡密没有客服功能权限'
                        ];
                    }
                    break;
                case 'product_listing':
                    if (!$has_product_listing) {
                        return [
                            'valid' => false,
                            'message' => '此卡密没有商品上架功能权限'
                        ];
                    }
                    break;
            }
        }
        
        // 检查是否有任何权限
        if (!$has_customer_service && !$has_product_listing) {
            return [
                'valid' => false,
                'message' => '此卡密没有任何功能权限'
            ];
        }
        
        return ['valid' => true];
    }
    
    /**
     * 更新过期卡密状态
     */
    private function updateExpiredLicense($license_id) {
        try {
            $stmt = $this->pdo->prepare("UPDATE license_keys SET status = 'expired' WHERE id = ?");
            $stmt->execute([$license_id]);
        } catch (Exception $e) {
            error_log("更新过期卡密状态失败: " . $e->getMessage());
        }
    }
    
    /**
     * 更新最后使用时间
     */
    private function updateLastUsed($license_id) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE license_keys 
                SET last_used_at = NOW(), 
                    last_heartbeat = NOW(),
                    last_used_ip = ?
                WHERE id = ?
            ");
            $stmt->execute([
                $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
                $license_id
            ]);
        } catch (Exception $e) {
            error_log("更新最后使用时间失败: " . $e->getMessage());
        }
    }
    
    /**
     * 【修复】获取状态消息 - 更精确的错误提示
     */
    private function getStatusMessage($status) {
        switch ($status) {
            case 'disabled':
                return '卡密已被禁用，请联系代理商';
            case 'expired':
                return '卡密已过期，请联系代理商续费';
            case 'banned':
                return '卡密已被封禁，请联系代理商';
            case 'suspended':
                return '卡密已被暂停使用，请联系代理商';
            case 'deleted':
                return '卡密已被删除，请联系代理商';
            case 'inactive':
                return '卡密未激活，请联系代理商';
            default:
                return '卡密状态异常，请联系代理商';
        }
    }

    /**
     * 【修复】获取状态对应的错误代码 - 更精确的错误分类
     */
    private function getStatusErrorCode($status) {
        switch ($status) {
            case 'disabled':
                return 'KEY_DISABLED';
            case 'expired':
                return 'KEY_EXPIRED';
            case 'banned':
                return 'KEY_BANNED';
            case 'suspended':
                return 'KEY_SUSPENDED';
            case 'deleted':
                return 'KEY_DELETED';
            case 'inactive':
                return 'KEY_INACTIVE';
            default:
                return 'KEY_INVALID_STATUS';
        }
    }
    
    /**
     * 创建成功响应
     */
    private function createSuccessResponse($license) {
        return [
            'success' => true,
            'license' => $license,
            'permissions' => [
                'has_customer_service' => (bool)($license['has_customer_service'] ?? 0),
                'has_product_listing' => (bool)($license['has_product_listing'] ?? 0)
            ]
        ];
    }
    
    /**
     * 创建错误响应
     */
    private function createErrorResponse($message, $error_code) {
        return [
            'success' => false,
            'message' => $message,
            'error_code' => $error_code
        ];
    }
    
    /**
     * 【修复】快速验证模式 - 专为APP登录优化，增强错误检测
     * 只检查基本状态，不更新使用时间，最快响应
     */
    public function quickValidate($license_key) {
        try {
            // 检查卡密格式
            if (empty($license_key) || strlen($license_key) > 255) {
                return $this->createErrorResponse('卡密格式不正确', 'INVALID_FORMAT');
            }

            // 【修复】查询更多必要字段，确保完整验证
            $stmt = $this->pdo->prepare("
                SELECT id, key_value, status, expiry_date, expires_at,
                       has_customer_service, has_product_listing, type,
                       store_name, wechat_store_id, is_multi_store
                FROM license_keys
                WHERE key_value = ?
                LIMIT 1
            ");
            $stmt->execute([$license_key]);
            $license = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$license) {
                // 【修复】记录不存在的卡密尝试
                $this->logSecurityEvent($license_key, 'KEY_NOT_FOUND');
                return $this->createErrorResponse('卡密不存在，请检查卡密是否正确', 'KEY_NOT_FOUND');
            }

            // 【修复】检查卡密状态 - 更精确的状态判断
            if ($license['status'] !== 'active') {
                // 记录禁用卡密的访问尝试
                $this->logSecurityEvent($license_key, 'KEY_DISABLED');

                $error_message = $this->getStatusMessage($license['status']);
                $error_code = $this->getStatusErrorCode($license['status']);
                return $this->createErrorResponse($error_message, $error_code);
            }

            // 【修复】快速检查过期时间
            $expiry_check = $this->checkExpiry($license);
            if (!$expiry_check['valid']) {
                // 自动更新过期卡密状态
                $this->updateExpiredLicense($license['id']);
                $this->logSecurityEvent($license_key, 'KEY_EXPIRED');
                return $this->createErrorResponse('卡密已过期，请联系代理商续费', 'KEY_EXPIRED');
            }

            // 【修复】检查功能权限 - 确保卡密有可用功能
            $has_customer_service = $license['has_customer_service'] ?? 0;
            $has_product_listing = $license['has_product_listing'] ?? 0;

            if (!$has_customer_service && !$has_product_listing) {
                $this->logSecurityEvent($license_key, 'NO_PERMISSIONS');
                return $this->createErrorResponse('此卡密没有任何功能权限', 'NO_PERMISSIONS');
            }

            return $this->createSuccessResponse($license);

        } catch (Exception $e) {
            error_log("快速验证异常: " . $e->getMessage());
            return $this->createErrorResponse('验证失败，请重试', 'SYSTEM_ERROR');
        }
    }

    /**
     * 强制验证模式 - 用于关键功能
     */
    public function strictValidate($license_key, $required_permissions = []) {
        // 【优化】对于APP登录，优先使用快速验证
        if (empty($required_permissions)) {
            return $this->quickValidate($license_key);
        }

        $result = $this->validateLicense($license_key, $required_permissions);

        if (!$result['success']) {
            // 在严格模式下，记录失败尝试
            error_log("严格验证失败: {$result['message']} (卡密: " . substr($license_key, 0, 10) . "...)");

            // 可以在这里添加更多安全措施，如IP限制等
            if ($this->strict_mode) {
                $this->logSecurityEvent($license_key, $result['error_code']);
            }
        }

        return $result;
    }

    /**
     * 脚本功能专用验证 - 静默拒绝模式
     */
    public function validateForScript($license_key, $required_permissions = []) {
        // 使用静默模式，不记录详细错误信息
        $result = $this->validateLicense($license_key, $required_permissions, true);

        // 对于脚本功能，任何验证失败都直接拒绝访问
        if (!$result['success']) {
            // 返回统一的拒绝消息，不暴露具体原因
            return [
                'success' => false,
                'message' => '访问被拒绝',
                'error_code' => 'ACCESS_DENIED'
            ];
        }

        return $result;
    }
    
    /**
     * 记录安全事件
     */
    private function logSecurityEvent($license_key, $error_code) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO security_logs (license_key, error_code, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                substr($license_key, 0, 20), // 只记录部分卡密
                $error_code,
                $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
                $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
            ]);
        } catch (Exception $e) {
            // 忽略日志记录错误
        }
    }
}

/**
 * 全局卡密验证函数 - 向后兼容
 */
function validateLicenseKey($license_key, $required_permissions = []) {
    global $pdo;
    
    if (!$pdo) {
        return [
            'success' => false,
            'message' => '数据库连接失败',
            'error_code' => 'DB_ERROR'
        ];
    }
    
    $middleware = new LicenseMiddleware($pdo);
    return $middleware->validateLicense($license_key, $required_permissions);
}

/**
 * 严格卡密验证函数 - 用于关键功能
 */
function strictValidateLicenseKey($license_key, $required_permissions = []) {
    global $pdo;
    
    if (!$pdo) {
        return [
            'success' => false,
            'message' => '数据库连接失败',
            'error_code' => 'DB_ERROR'
        ];
    }
    
    $middleware = new LicenseMiddleware($pdo, true);
    return $middleware->strictValidate($license_key, $required_permissions);
}
