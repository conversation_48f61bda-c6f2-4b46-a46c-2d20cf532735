<?php
/**
 * 测试简化的verify.php验证系统
 */

// 模拟POST请求测试
function testVerifyAPI($key, $check_status = 0) {
    $url = 'http://localhost/api/verify.php';
    
    $data = [
        'key' => $key,
        'check_status' => $check_status
    ];
    
    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    return json_decode($result, true);
}

echo "=== 测试简化的verify.php验证系统 ===\n\n";

// 测试1: 空卡密
echo "测试1: 空卡密\n";
$result = testVerifyAPI('');
echo "结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n\n";

// 测试2: 无效卡密
echo "测试2: 无效卡密\n";
$result = testVerifyAPI('invalid_key_123');
echo "结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n\n";

// 测试3: 状态检查（需要有效卡密）
echo "测试3: 状态检查\n";
$result = testVerifyAPI('test_key', 1);
echo "结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n\n";

echo "=== 测试完成 ===\n";
?>
