<?php
/**
 * 直接测试verify.php的核心逻辑
 */

// 模拟POST数据 - 使用真实的卡密
$_POST['key'] = 'XMHS-05E9E3CBDA6A643F3427';
$_POST['check_status'] = '1';

// 设置输出缓冲
ob_start();

echo "=== 直接测试verify.php核心逻辑 ===\n\n";

try {
    // 包含必要的文件
    require_once 'includes/db.php';
    
    if (!$pdo) {
        echo "数据库连接失败\n";
        exit;
    }
    
    echo "数据库连接成功\n";
    
    // 测试基本的卡密查询
    $key = 'XMHS-05E9E3CBDA6A643F3427';
    $stmt = $pdo->prepare("
        SELECT id, key_value, status, expiry_date, expires_at,
               has_customer_service, has_product_listing, type,
               store_name, wechat_store_id, is_multi_store
        FROM license_keys
        WHERE key_value = ?
        LIMIT 1
    ");
    $stmt->execute([$key]);
    $license = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($license) {
        echo "找到卡密记录:\n";
        echo "- ID: " . $license['id'] . "\n";
        echo "- 状态: " . $license['status'] . "\n";
        echo "- 过期时间: " . ($license['expiry_date'] ?: $license['expires_at']) . "\n";
        echo "- 客服功能: " . ($license['has_customer_service'] ? '是' : '否') . "\n";
        echo "- 商品上架功能: " . ($license['has_product_listing'] ? '是' : '否') . "\n";
    } else {
        echo "未找到卡密记录\n";
        
        // 查看数据库中有哪些卡密
        $stmt = $pdo->prepare("SELECT key_value, status FROM license_keys LIMIT 5");
        $stmt->execute();
        $keys = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "数据库中的卡密示例:\n";
        foreach ($keys as $key_info) {
            echo "- " . $key_info['key_value'] . " (状态: " . $key_info['status'] . ")\n";
        }
    }
    
    // 测试脚本查询
    echo "\n测试脚本查询:\n";
    $script_stmt = $pdo->prepare("
        SELECT script_code, name, version
        FROM scripts
        WHERE status = 'active' AND (has_wechat_store = 1 OR has_douyin_store = 1)
        ORDER BY updated_at DESC
        LIMIT 1
    ");
    $script_stmt->execute();
    $script_row = $script_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($script_row) {
        echo "找到脚本记录:\n";
        echo "- 名称: " . $script_row['name'] . "\n";
        echo "- 版本: " . $script_row['version'] . "\n";
        echo "- 脚本大小: " . strlen($script_row['script_code']) . " 字节\n";
    } else {
        echo "未找到脚本记录\n";
    }
    
} catch (Exception $e) {
    echo "测试过程中发生错误: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";

// 获取输出内容
$output = ob_get_clean();
echo $output;

// 同时写入日志文件
file_put_contents('test_verify_result.log', $output);
?>
