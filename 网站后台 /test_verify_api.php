<?php
/**
 * 完整测试verify.php API
 */

echo "=== 完整测试verify.php API ===\n\n";

// 测试1: 状态检查
echo "测试1: 状态检查\n";
$_POST = ['key' => 'XMHS-05E9E3CBDA6A643F3427', 'check_status' => '1'];
ob_start();
include 'api/verify.php';
$result1 = ob_get_clean();
echo "结果: " . $result1 . "\n\n";

// 测试2: 获取脚本
echo "测试2: 获取脚本\n";
$_POST = ['key' => 'XMHS-05E9E3CBDA6A643F3427'];
ob_start();
include 'api/verify.php';
$result2 = ob_get_clean();
$data = json_decode($result2, true);
if ($data && $data['success']) {
    echo "成功获取脚本，大小: " . strlen($data['script']) . " 字节\n";
    echo "功能类型: " . $data['function_type'] . "\n";
    echo "过期时间: " . $data['expiry_date'] . "\n";
} else {
    echo "结果: " . $result2 . "\n";
}
echo "\n";

// 测试3: 无效卡密
echo "测试3: 无效卡密\n";
$_POST = ['key' => 'invalid_key_123'];
ob_start();
include 'api/verify.php';
$result3 = ob_get_clean();
echo "结果: " . $result3 . "\n\n";

// 测试4: 空卡密
echo "测试4: 空卡密\n";
$_POST = ['key' => ''];
ob_start();
include 'api/verify.php';
$result4 = ob_get_clean();
echo "结果: " . $result4 . "\n\n";

echo "=== 测试完成 ===\n";
?>
