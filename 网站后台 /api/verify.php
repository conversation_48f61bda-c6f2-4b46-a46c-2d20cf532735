<?php
/**
 * 极简卡密验证API
 * 简化版本，只进行基本的卡密验证
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db.php';

// 禁用错误显示到页面，但记录到日志
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 获取客户端IP地址
function get_client_ip() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    foreach ($ip_keys as $key) {
        if (!empty($_SERVER[$key])) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = trim(explode(',', $ip)[0]);
            }
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

// 简单的日志记录
function logApiCall($key, $ip, $status, $message) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("INSERT INTO api_logs (license_key, ip_address, status, message, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute([substr($key, 0, 255), substr($ip, 0, 45), substr($status, 0, 20), substr($message, 0, 1000)]);
    } catch (Exception $e) {
        error_log("日志记录失败: " . $e->getMessage());
    }
}

// 获取POST输入
$key = trim($_POST['key'] ?? '');
$check_status = trim($_POST['check_status'] ?? '');

// 获取客户端IP地址
$client_ip = get_client_ip();

// 基本输入验证
if (empty($key)) {
    logApiCall('', $client_ip, 'error', '卡密不能为空');
    echo json_encode([
        'success' => false,
        'message' => '卡密不能为空',
        'error_code' => 'MISSING_KEY'
    ]);
    exit();
}

if (strlen($key) > 255) {
    logApiCall(substr($key, 0, 20) . '...', $client_ip, 'error', '卡密格式无效');
    echo json_encode([
        'success' => false,
        'message' => '卡密格式无效',
        'error_code' => 'INVALID_FORMAT'
    ]);
    exit();
}

try {
    // 极简验证：直接查询数据库
    $stmt = $pdo->prepare("
        SELECT id, key_value, status, expiry_date, expires_at,
               has_customer_service, has_product_listing, type,
               store_name, wechat_store_id, is_multi_store
        FROM license_keys
        WHERE key_value = ?
        LIMIT 1
    ");
    $stmt->execute([$key]);
    $license = $stmt->fetch(PDO::FETCH_ASSOC);

    // 检查卡密是否存在
    if (!$license) {
        logApiCall($key, $client_ip, 'error', '卡密不存在');
        echo json_encode([
            'success' => false,
            'message' => '卡密不存在，请检查卡密是否正确',
            'error_code' => 'KEY_NOT_FOUND'
        ]);
        exit();
    }

    // 检查卡密状态
    if ($license['status'] !== 'active') {
        logApiCall($key, $client_ip, 'error', '卡密已禁用');
        echo json_encode([
            'success' => false,
            'message' => '卡密已被禁用，请联系代理商',
            'error_code' => 'KEY_DISABLED'
        ]);
        exit();
    }

    // 检查过期时间
    $expiry_date = $license['expiry_date'] ?: $license['expires_at'];
    if ($expiry_date && strtotime($expiry_date) < time()) {
        logApiCall($key, $client_ip, 'error', '卡密已过期');
        echo json_encode([
            'success' => false,
            'message' => '卡密已过期，请联系代理商续费',
            'error_code' => 'KEY_EXPIRED'
        ]);
        exit();
    }

    // 检查功能权限
    $has_customer_service = $license['has_customer_service'] ?? 0;
    $has_product_listing = $license['has_product_listing'] ?? 0;

    if (!$has_customer_service && !$has_product_listing) {
        logApiCall($key, $client_ip, 'error', '无功能权限');
        echo json_encode([
            'success' => false,
            'message' => '此卡密没有任何功能权限',
            'error_code' => 'NO_PERMISSIONS'
        ]);
        exit();
    }

    // 获取店铺信息
    $stores = [];
    if (!empty($license['store_name']) || !empty($license['wechat_store_id'])) {
        $stores[] = [
            'store_name' => $license['store_name'],
            'wechat_store_id' => $license['wechat_store_id']
        ];
    }

    // 如果是多店铺卡密，获取额外店铺信息
    if ($license['is_multi_store']) {
        try {
            $stores_stmt = $pdo->prepare("SELECT store_name, wechat_store_id FROM license_key_stores WHERE license_key_id = ?");
            $stores_stmt->execute([$license['id']]);
            $additional_stores = $stores_stmt->fetchAll(PDO::FETCH_ASSOC);
            if (!empty($additional_stores)) {
                $stores = array_merge($stores, $additional_stores);
            }
        } catch (Exception $e) {
            error_log("获取额外店铺信息失败: " . $e->getMessage());
        }
    }

    // 如果是状态检查请求，只返回状态信息
    if ($check_status === '1') {
        $function_type = '';
        if ($has_customer_service && $has_product_listing) {
            $function_type = 'full_features';
        } elseif ($has_product_listing && !$has_customer_service) {
            $function_type = 'product_listing';
        } elseif ($has_customer_service && !$has_product_listing) {
            $function_type = 'customer_service';
        } else {
            $function_type = 'no_features';
        }

        logApiCall($key, $client_ip, 'success', "状态检查成功");
        echo json_encode([
            'success' => true,
            'message' => '卡密状态正常',
            'function_type' => $function_type,
            'has_customer_service' => (bool)$has_customer_service,
            'has_product_listing' => (bool)$has_product_listing,
            'expiry_date' => $expiry_date,
            'status' => $license['status'],
            'stores' => $stores,
            'is_multi_store' => (bool)$license['is_multi_store']
        ]);
        exit();
    }

    // 获取脚本代码
    $script_conditions = [];
    if ($has_customer_service) {
        $script_conditions[] = "has_wechat_store = 1";
    }
    if ($has_product_listing) {
        $script_conditions[] = "has_douyin_store = 1";
    }

    if (empty($script_conditions)) {
        logApiCall($key, $client_ip, 'error', '无可用功能');
        echo json_encode(['success' => false, 'message' => '此卡密没有可用功能']);
        exit();
    }

    $script_sql = "
        SELECT script_code, name, version
        FROM scripts
        WHERE status = 'active' AND (" . implode(' OR ', $script_conditions) . ")
        ORDER BY updated_at DESC
        LIMIT 1
    ";

    $script_stmt = $pdo->prepare($script_sql);
    $script_stmt->execute();
    $script_row = $script_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$script_row) {
        logApiCall($key, $client_ip, 'error', '找不到脚本');
        echo json_encode(['success' => false, 'message' => '找不到对应功能的脚本']);
        exit();
    }

    $script_code = $script_row['script_code'];
    if (empty($script_code)) {
        logApiCall($key, $client_ip, 'error', '脚本代码为空');
        echo json_encode(['success' => false, 'message' => '脚本代码为空']);
        exit();
    }


    // 更新使用记录
    try {
        $update_stmt = $pdo->prepare("UPDATE license_keys SET last_used_ip = ?, last_heartbeat = NOW() WHERE id = ?");
        $update_stmt->execute([$client_ip, $license['id']]);
    } catch (Exception $e) {
        error_log("更新使用记录失败: " . $e->getMessage());
    }

    // 确定功能类型和消息
    $function_type = '';
    $success_message = '';

    if ($has_customer_service && $has_product_listing) {
        $function_type = 'full_features';
        $success_message = '卡密验证通过，恭喜您已成功开通"小梅花AI客服-微信小店+抖店"';
    } elseif ($has_product_listing && !$has_customer_service) {
        $function_type = 'product_listing';
        $success_message = '卡密验证通过，恭喜您已成功开通"小梅花AI客服-抖店"';
    } elseif ($has_customer_service && !$has_product_listing) {
        $function_type = 'customer_service';
        $success_message = '卡密验证通过，恭喜您已成功开通"小梅花AI客服-微信小店"';
    } else {
        $function_type = 'no_features';
        $success_message = '卡密验证通过，但此卡密未开通任何功能';
    }

    // 提取@match规则
    $match_urls = [];
    if (preg_match_all('/@match\s+(.+)/i', $script_code, $matches)) {
        foreach ($matches[1] as $match) {
            $url = trim($match);
            if (!empty($url)) {
                $match_urls[] = $url;
            }
        }
    }

    logApiCall($key, $client_ip, 'success', "验证成功 - {$function_type}");

    // 返回成功响应
    echo json_encode([
        'success' => true,
        'script' => $script_code,
        'match_urls' => $match_urls,
        'expiry_date' => $expiry_date,
        'type' => $license['type'] ?? 'day',
        'store_name' => $license['store_name'] ?? '',
        'wechat_store_id' => $license['wechat_store_id'] ?? '',
        'stores' => $stores,
        'is_multi_store' => (bool)$license['is_multi_store'],
        'function_type' => $function_type,
        'message' => $success_message,
        'has_customer_service' => (bool)$has_customer_service,
        'has_product_listing' => (bool)$has_product_listing
    ]);

} catch (PDOException $e) {
    error_log("数据库错误: " . $e->getMessage());
    logApiCall($key, $client_ip, 'error', '数据库错误');
    echo json_encode([
        'success' => false,
        'message' => '系统错误，请稍后重试',
        'error_code' => 'SYSTEM_ERROR'
    ]);
} catch (Exception $e) {
    error_log("系统异常: " . $e->getMessage());
    logApiCall($key, $client_ip, 'error', '系统异常');
    echo json_encode([
        'success' => false,
        'message' => '系统异常，请稍后重试',
        'error_code' => 'SYSTEM_ERROR'
    ]);
}
?>