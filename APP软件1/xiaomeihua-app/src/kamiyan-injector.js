const fs = require('fs');
const path = require('path');
const { createCrossPageEnvironment, createAutoPageOpenerHelpers } = require('./cross-page-injector');
const { app } = require('electron');

// URL匹配函数，支持Tampermonkey的匹配规则
function matchUrl(url, patterns) {
  if (!patterns || !Array.isArray(patterns) || patterns.length === 0) {
    // 【关键修改】如果没有匹配规则，严格返回false，不允许执行
    console.log('⚠️ 没有URL匹配规则，严格模式下拒绝执行');
    return false;
  }

  console.log('🔍 开始URL匹配检查:', { url, patterns });

  for (const pattern of patterns) {
    if (isUrlMatch(url, pattern)) {
      console.log('✅ URL匹配成功:', pattern);
      return true;
    }
  }

  console.log('❌ URL不匹配任何规则');
  return false;
}

// 检查单个URL是否匹配模式
function isUrlMatch(url, pattern) {
  if (!url || !pattern) {
    console.log('❌ URL或模式为空:', { url, pattern });
    return false;
  }

  console.log('🔍 检查URL匹配:', { url, pattern });

  // 通配符 * 匹配所有
  if (pattern === '*') {
    console.log('✅ 通配符匹配');
    return true;
  }

  // 简单字符串包含匹配（优先）
  if (url.includes(pattern)) {
    console.log('✅ 字符串包含匹配');
    return true;
  }

  try {
    // 将Tampermonkey的匹配规则转换为正则表达式
    let regexPattern = pattern
      .replace(/\./g, '\\.')  // 转义点号
      .replace(/\*/g, '.*')   // * 转换为 .*
      .replace(/\?/g, '\\?'); // 转义问号

    // 处理协议通配符 *://
    regexPattern = regexPattern.replace(/^\.\*:\/\//, '(https?|ftp|file)://');

    // 如果模式不以协议开头，添加协议匹配
    if (!regexPattern.match(/^(https?|ftp|file|\.\*)/)) {
      regexPattern = '(https?://)?' + regexPattern;
    }

    const regex = new RegExp('^' + regexPattern + '$', 'i');
    const isMatch = regex.test(url);

    console.log('🔍 正则匹配结果:', {
      regexPattern,
      isMatch,
      url,
      pattern
    });

    return isMatch;
  } catch (error) {
    console.error('URL匹配规则解析错误:', error, 'Pattern:', pattern);
    return false;
  }
}

// 读取卡密验证脚本
function getKamiyanScript() {
  try {
    // 首先尝试从资源目录读取
    const resourcePath = path.join(process.resourcesPath, 'resources', 'kamiyanzheng.js');
    
    // 如果在开发环境，则从项目目录读取
    const devPath = path.join(__dirname, '../resources/kamiyanzheng.js');
    
    // 尝试从上级目录读取（针对打包后的路径结构）
    const parentPath = path.join(__dirname, '../../kamiyanzheng.js');
    
    // 添加更多可能的路径
    const additionalPaths = [
      path.join(__dirname, '../kamiyanzheng.js'),
      path.join(app ? app.getAppPath() : __dirname, 'kamiyanzheng.js'),
      path.join(app ? app.getAppPath() : __dirname, 'resources', 'kamiyanzheng.js')
    ];
    
    // 合并所有可能的路径
    const allPaths = [resourcePath, devPath, parentPath, ...additionalPaths];
    
    // 查找第一个存在的文件
    let scriptPath;
    for (const p of allPaths) {
      if (fs.existsSync(p)) {
        scriptPath = p;
        console.log(`从路径加载卡密验证脚本: ${p}`);
        break;
      }
    }
    
    if (!scriptPath) {
      throw new Error('卡密验证脚本文件不存在');
    }
    
    const script = fs.readFileSync(scriptPath, 'utf-8');
    console.log(`成功读取卡密验证脚本，大小: ${script.length} 字节`);
    return script;
  } catch (error) {
    console.error('读取卡密验证脚本失败:', error);
    return null;
  }
}

// 获取xinkami.js脚本
function getXinkamiScript() {
  try {
    // 查找xinkami.js的所有可能路径
    const xinkamiPaths = [
      path.join(process.resourcesPath, 'resources', 'xinkami.js'),
      path.join(__dirname, '../resources/xinkami.js'),
      path.join(__dirname, '../../xinkami.js'),
      path.join(__dirname, '../xinkami.js'),
      path.join(app ? app.getAppPath() : __dirname, 'xinkami.js'),
      path.join(app ? app.getAppPath() : __dirname, 'resources', 'xinkami.js')
    ];
    
    // 查找第一个存在的文件
    let scriptPath;
    for (const p of xinkamiPaths) {
      if (fs.existsSync(p)) {
        scriptPath = p;
        console.log(`从路径加载xinkami.js脚本: ${p}`);
        break;
      }
    }
    
    if (!scriptPath) {
      console.error('xinkami.js脚本文件不存在');
      return null;
    }
    
    const script = fs.readFileSync(scriptPath, 'utf-8');
    console.log(`成功读取xinkami.js脚本，大小: ${script.length} 字节`);
    
    // 修改脚本中的API端点，将xinkami.js替换为verify.php
    const modifiedScript = script.replace(
      /https:\/\/xiaomeihuakefu\.cn\/api\/xinkami\.js/g, 
      'https://xiaomeihuakefu.cn/api/verify.php'
    );
    
    console.log('已修改脚本中的API端点为verify.php');
    
    return modifiedScript;
  } catch (error) {
    console.error('读取xinkami.js脚本失败:', error);
    return null;
  }
}

// 获取完整的tampermonkey浏览器脚本
function getTampermonkeyScript() {
  try {
    // 查找tampermonkey浏览器脚本的所有可能路径
    const tampermonkeyPaths = [
      path.join(process.resourcesPath, 'resources', 'tampermonkey浏览器脚本.js'),
      path.join(__dirname, '../resources/tampermonkey浏览器脚本.js'),
      path.join(__dirname, '../../tampermonkey浏览器脚本.js'),
      path.join(__dirname, '../tampermonkey浏览器脚本.js'),
      path.join(app ? app.getAppPath() : __dirname, 'tampermonkey浏览器脚本.js'),
      path.join(app ? app.getAppPath() : __dirname, 'resources', 'tampermonkey浏览器脚本.js')
    ];

    // 查找第一个存在的文件
    let scriptPath;
    for (const p of tampermonkeyPaths) {
      if (fs.existsSync(p)) {
        scriptPath = p;
        console.log(`从路径加载tampermonkey浏览器脚本: ${p}`);
        break;
      }
    }

    if (!scriptPath) {
      console.error('tampermonkey浏览器脚本文件不存在');
      return null;
    }

    const script = fs.readFileSync(scriptPath, 'utf-8');
    console.log(`成功读取tampermonkey浏览器脚本，大小: ${script.length} 字节`);
    return script;
  } catch (error) {
    console.error('读取tampermonkey浏览器脚本失败:', error);
    return null;
  }
}





// 创建注入脚本
function createInjectionScript(licenseKey, shopInfo) {
  // 尝试获取完整的tampermonkey浏览器脚本
  const tampermonkeyScript = getTampermonkeyScript();
  
  if (tampermonkeyScript) {
    console.log('使用完整的tampermonkey浏览器脚本');
    
    // 提取需要传递的店铺信息
    const shopData = shopInfo || {};
    const shopName = shopData.shopName || '未知店铺';
    const shopId = shopData.shopId || '';
    const wechatStoreId = shopData.wechatStoreId || '';
    
    // 创建API模拟脚本
    const apiScript = `
      // 设置全局卡密变量
      window.xiaomeihuaLicenseKey = "${licenseKey}";
      
      // 设置店铺信息
      window.xiaomeihuaShopInfo = {
        shopName: "${shopName}",
        shopId: "${shopId}",
        wechatStoreId: "${wechatStoreId}"
      };
      
      // 创建API配置对象
      window.API_CONFIG = {
        endpoints: {
          script: '/api/verify.php',
          verify: '/api/verify.php',
          data: '/api/data.php'
        },
        baseUrl: 'https://xiaomeihuakefu.cn',
        timeout: 10000,
        retries: 3
      };
      
      // 创建全局API存储对象
      window._tampermonkeyAPIs = {};
      
      // 完整模拟Tampermonkey API
      window.GM_setValue = function(key, value) {
        localStorage.setItem('xiaomeihua_' + key, JSON.stringify(value));
        window._tampermonkeyAPIs.GM_setValue = window.GM_setValue;
        return true;
      };
      
      window.GM_getValue = function(key, defaultValue) {
        const value = localStorage.getItem('xiaomeihua_' + key);
        window._tampermonkeyAPIs.GM_getValue = window.GM_getValue;
        return value !== null ? JSON.parse(value) : defaultValue;
      };
      
      window.GM_deleteValue = function(key) {
        localStorage.removeItem('xiaomeihua_' + key);
        window._tampermonkeyAPIs.GM_deleteValue = window.GM_deleteValue;
        return true;
      };
      
      window.GM_xmlhttpRequest = function(options) {
        const xhr = new XMLHttpRequest();
        xhr.open(options.method || 'GET', options.url, true);
        
        if (options.headers) {
          Object.keys(options.headers).forEach(key => {
            xhr.setRequestHeader(key, options.headers[key]);
          });
        }
        
        xhr.setRequestHeader('X-Xiaomeihua-License', "${licenseKey}");
        
        xhr.onload = function() {
          if (typeof options.onload === 'function') {
            options.onload({
              status: xhr.status,
              statusText: xhr.statusText,
              responseText: xhr.responseText,
              responseHeaders: xhr.getAllResponseHeaders(),
              response: xhr.response,
              finalUrl: options.url
            });
          }
        };
        
        xhr.onerror = function() {
          if (typeof options.onerror === 'function') {
            options.onerror(new Error('Network error'));
          }
        };
        
        xhr.send(options.data);
        
        window._tampermonkeyAPIs.GM_xmlhttpRequest = window.GM_xmlhttpRequest;
        
        return {
          abort: function() {
            xhr.abort();
          }
        };
      };
      
      window.GM_addStyle = function(css) {
        const style = document.createElement('style');
        style.textContent = css;
        document.head.appendChild(style);
        window._tampermonkeyAPIs.GM_addStyle = window.GM_addStyle;
        return style;
      };
      
      window.GM_openInTab = function(url, options) {
        window._tampermonkeyAPIs.GM_openInTab = window.GM_openInTab;
        window.open(url, '_blank');
        return null;
      };
      
      window.unsafeWindow = window;
      
      // 初始化店铺信息到本地存储
      window.GM_setValue('saved_license_key_xiaomeihua', "${licenseKey}");
      
      console.log('小梅花AI脚本API设置完成');
      
      // 【新增】添加直接执行函数
      window.xiaomeihuaForceInit = function() {
        console.log('强制初始化小梅花脚本...');
        
        // 检查是否有main函数
        if (typeof window.main === 'function') {
          console.log('调用main函数...');
          window.main();
        } else {
          console.log('未找到main函数');
        }
        
        // 检查是否有createFloatingIcon函数
        if (typeof window.createFloatingIcon === 'function') {
          console.log('调用createFloatingIcon函数...');
          window.createFloatingIcon();
        }
        
        // 检查是否有createControlPanel函数
        if (typeof window.createControlPanel === 'function') {
          console.log('调用createControlPanel函数...');
          window.createControlPanel();
        }
        
        // 检查是否有restorePanelState函数
        if (typeof window.restorePanelState === 'function') {
          console.log('调用restorePanelState函数...');
          window.restorePanelState();
        }
        
        return '强制初始化完成';
      };
      
      // 【新增】添加强制显示浮动图标的函数
      window.forceShowFloatingIcon = function() {
        console.log('强制显示浮动图标...');
        
        // 检查浮动图标是否存在
        let icon = document.getElementById('floating-icon');
        
        if (!icon) {
          console.log('浮动图标不存在，尝试创建...');
          
          // 尝试创建浮动图标
          if (typeof window.createFloatingIcon === 'function') {
            icon = window.createFloatingIcon();
          } else {
            console.log('无法创建浮动图标：createFloatingIcon函数不存在');
            return '无法创建浮动图标';
          }
        }
        
        // 强制设置样式确保可见
        icon.style.cssText = \`
          position: fixed !important;
          top: 20px;
          left: 20px;
          width: 60px;
          height: 60px;
          border-radius: 50%;
          cursor: pointer;
          z-index: 2147483647 !important;
          display: block !important;
          opacity: 1 !important;
          transform: scale(1);
          background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382);
          background-size: 300% 300%;
          visibility: visible !important;
          pointer-events: auto !important;
        \`;
        
        // 确保图标在DOM中
        if (!document.body.contains(icon)) {
          document.body.appendChild(icon);
        }
        
        // 重新添加到body末尾确保在最上层
        document.body.appendChild(icon);
        
        // 添加样式修复
        const style = document.createElement('style');
        style.textContent = \`
          #floating-icon {
            position: fixed !important;
            z-index: 2147483647 !important;
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            pointer-events: auto !important;
          }
        \`;
        document.head.appendChild(style);
        
        return '浮动图标已强制显示';
      };
      
      // 【修复】页面加载完成后的初始化逻辑 - 移除无条件脚本注入
      window.addEventListener('load', function() {
        console.log('页面加载完成，准备初始化API环境...');

        // 只设置API环境，不直接注入脚本
        // 脚本注入应该由外部的URL匹配逻辑控制

        setTimeout(function() {
          console.log('✅ 小梅花API环境初始化完成');
          console.log('⚠️ 脚本注入由URL匹配逻辑控制');

          // 触发自定义事件，通知外部API环境已准备就绪
          const event = new CustomEvent('xiaomeihuaApiReady', {
            detail: {
              licenseKey: "${licenseKey}",
              shopInfo: window.xiaomeihuaShopInfo
            }
          });
          window.dispatchEvent(event);
        }, 500);
      });
    `;
    
    return apiScript;
  }
  
  // 如果没有找到tampermonkey浏览器脚本，回退到使用kamiyanScript
  const kamiyanScript = getKamiyanScript();
  const xinkamiScript = getXinkamiScript();
  
  if (!kamiyanScript && !xinkamiScript) {
    console.error('无法获取任何脚本');
    return null;
  }
  
  // 提取需要传递的店铺信息
  const shopData = shopInfo || {};
  const shopName = shopData.shopName || '未知店铺';
  const shopId = shopData.shopId || '';
  const wechatStoreId = shopData.wechatStoreId || '';
  const expireDate = shopData.expireDate || '';
  const hasCustomerService = shopData.hasCustomerService || false;
  const hasProductListing = shopData.hasProductListing || false;
  const functionType = shopData.functionType || '';
  
  console.log('创建注入脚本，卡密:', licenseKey.substring(0, 8) + '...');
  console.log('店铺信息:', {shopName, shopId, wechatStoreId, functionType});
  
  // 创建注入脚本
  return `
    // 设置全局卡密变量
    window.xiaomeihuaLicenseKey = "${licenseKey}";

    // 设置店铺信息
    window.xiaomeihuaShopInfo = {
      shopName: "${shopName}",
      shopId: "${shopId}",
      wechatStoreId: "${wechatStoreId}",
      expireDate: "${expireDate}",
      hasCustomerService: ${hasCustomerService},
      hasProductListing: ${hasProductListing},
      functionType: "${functionType}"
    };

    // 【新增】注入跨页面执行环境
    ${createCrossPageEnvironment({
      type: functionType,
      shopId: shopId,
      capabilities: ['script-execution', 'auto-page-opener', 'douyin-customer-service']
    })}

    // 【新增】注入自动页面打开辅助函数
    ${createAutoPageOpenerHelpers()}
    
    // 创建API配置对象
    window.API_CONFIG = {
      endpoints: {
        script: '/api/verify.php',
        verify: '/api/verify.php',
        data: '/api/data.php'
      },
      baseUrl: 'https://xiaomeihuakefu.cn',
      timeout: 10000,
      retries: 3
    };
    
    // 创建全局API存储对象
    window._tampermonkeyAPIs = {};
    
    // 完整模拟Tampermonkey API
    
    // 存储API
    window.GM_setValue = function(key, value) {
      console.log('GM_setValue:', key);
      localStorage.setItem('xiaomeihua_' + key, JSON.stringify(value));
      window._tampermonkeyAPIs.GM_setValue = window.GM_setValue;
      return true;
    };
    
    window.GM_getValue = function(key, defaultValue) {
      console.log('GM_getValue:', key);
      const value = localStorage.getItem('xiaomeihua_' + key);
      window._tampermonkeyAPIs.GM_getValue = window.GM_getValue;
      return value !== null ? JSON.parse(value) : defaultValue;
    };
    
    window.GM_deleteValue = function(key) {
      console.log('GM_deleteValue:', key);
      localStorage.removeItem('xiaomeihua_' + key);
      window._tampermonkeyAPIs.GM_deleteValue = window.GM_deleteValue;
      return true;
    };
    
    window.GM_listValues = function() {
      console.log('GM_listValues');
      const keys = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key.startsWith('xiaomeihua_')) {
          keys.push(key.substring(11));
        }
      }
      window._tampermonkeyAPIs.GM_listValues = window.GM_listValues;
      return keys;
    };
    
    // 网络请求API
    window.GM_xmlhttpRequest = function(options) {
      console.log('GM_xmlhttpRequest:', options.url);
      
      // 修正URL，如果是访问xinkami.js，则改为verify.php
      if (options.url && options.url.includes('/api/xinkami.js')) {
        console.log('检测到xinkami.js请求，修正为verify.php');
        options.url = options.url.replace('/api/xinkami.js', '/api/verify.php');
      }
      
      const xhr = new XMLHttpRequest();
      xhr.open(options.method || 'GET', options.url, true);
      
      if (options.headers) {
        Object.keys(options.headers).forEach(key => {
          xhr.setRequestHeader(key, options.headers[key]);
        });
      }
      
      // 添加卡密和店铺信息到请求头
      xhr.setRequestHeader('X-Xiaomeihua-License', "${licenseKey}");
      xhr.setRequestHeader('X-Xiaomeihua-Shop-ID', "${shopId}");
      xhr.setRequestHeader('X-Xiaomeihua-Wechat-Store-ID', "${wechatStoreId}");
      xhr.setRequestHeader('X-Xiaomeihua-Function-Type', "${functionType}");
      
      if (options.timeout) {
        xhr.timeout = options.timeout;
      }
      
      xhr.onload = function() {
        if (typeof options.onload === 'function') {
          options.onload({
            status: xhr.status,
            statusText: xhr.statusText,
            responseText: xhr.responseText,
            responseHeaders: xhr.getAllResponseHeaders(),
            response: xhr.response,
            responseXML: xhr.responseXML,
            readyState: xhr.readyState,
            finalUrl: options.url
          });
        }
      };
      
      xhr.onerror = function() {
        if (typeof options.onerror === 'function') {
          options.onerror(new Error('Network error'));
        }
      };
      
      xhr.ontimeout = function() {
        if (typeof options.ontimeout === 'function') {
          options.ontimeout(new Error('Request timed out'));
        }
      };
      
      xhr.onabort = function() {
        if (typeof options.onabort === 'function') {
          options.onabort(new Error('Request aborted'));
        }
      };
      
      xhr.onreadystatechange = function() {
        if (typeof options.onreadystatechange === 'function') {
          options.onreadystatechange(xhr);
        }
      };
      
      xhr.send(options.data);
      
      window._tampermonkeyAPIs.GM_xmlhttpRequest = window.GM_xmlhttpRequest;
      
      return {
        abort: function() {
          xhr.abort();
        }
      };
    };
    
    // UI相关API
    window.GM_addStyle = function(css) {
      console.log('GM_addStyle');
      const style = document.createElement('style');
      style.textContent = css;
      document.head.appendChild(style);
      window._tampermonkeyAPIs.GM_addStyle = window.GM_addStyle;
      return style;
    };
    
    // 浏览器交互API
    window.GM_openInTab = function(url, options) {
      console.log('GM_openInTab:', url);
      window._tampermonkeyAPIs.GM_openInTab = window.GM_openInTab;
      window.open(url, '_blank');
      return null;
    };
    
    window.unsafeWindow = window;
    
    // 初始化店铺信息到本地存储
    window.GM_setValue('saved_license_key_xiaomeihua', "${licenseKey}");
    
    console.log('小梅花AI脚本API设置完成');
    
    // 【新增】添加直接执行函数
    window.xiaomeihuaForceInit = function() {
      console.log('强制初始化小梅花脚本...');
      
      // 检查是否有main函数
      if (typeof window.main === 'function') {
        console.log('调用main函数...');
        window.main();
      }
      
      // 延迟执行其他初始化函数
      setTimeout(function() {
        // 检查是否有createFloatingIcon函数
        if (typeof window.createFloatingIcon === 'function') {
          console.log('调用createFloatingIcon函数...');
          window.createFloatingIcon();
        }
        
        // 检查是否有createControlPanel函数
        if (typeof window.createControlPanel === 'function') {
          console.log('调用createControlPanel函数...');
          window.createControlPanel();
        }
        
        // 检查是否有restorePanelState函数
        if (typeof window.restorePanelState === 'function') {
          console.log('调用restorePanelState函数...');
          window.restorePanelState();
        }
      }, 1000);
      
      return '强制初始化完成';
    };
    
    // 【新增】添加强制显示浮动图标的函数
    window.forceShowFloatingIcon = function() {
      console.log('强制显示浮动图标...');
      
      // 检查浮动图标是否存在
      let icon = document.getElementById('floating-icon');
      
      if (!icon) {
        console.log('浮动图标不存在，尝试创建...');
        
        // 尝试创建浮动图标
        if (typeof window.createFloatingIcon === 'function') {
          icon = window.createFloatingIcon();
        } else {
          console.log('无法创建浮动图标：createFloatingIcon函数不存在');
          return '无法创建浮动图标';
        }
      }
      
      // 强制设置样式确保可见
      icon.style.cssText = \`
        position: fixed !important;
        top: 20px;
        left: 20px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        cursor: pointer;
        z-index: 2147483647 !important;
        display: block !important;
        opacity: 1 !important;
        transform: scale(1);
        background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500, #e55039, #3c6382);
        background-size: 300% 300%;
        visibility: visible !important;
        pointer-events: auto !important;
      \`;
      
      // 确保图标在DOM中
      if (!document.body.contains(icon)) {
        document.body.appendChild(icon);
      }
      
      // 重新添加到body末尾确保在最上层
      document.body.appendChild(icon);
      
      // 添加样式修复
      const style = document.createElement('style');
      style.textContent = \`
        #floating-icon {
          position: fixed !important;
          z-index: 2147483647 !important;
          display: block !important;
          opacity: 1 !important;
          visibility: visible !important;
          pointer-events: auto !important;
        }
      \`;
      document.head.appendChild(style);
      
      return '浮动图标已强制显示';
    };
    
    // 【重要修改】移除无条件的自动执行逻辑
    // 之前的代码会在页面加载时自动执行脚本，现在改为由外部URL匹配逻辑控制
    // 只设置API环境，脚本注入由main.js中的URL匹配逻辑决定
    
    // 提供脚本执行接口，供URL匹配成功后调用
    window.xiaomeihuaExecuteScript = function(scriptContent) {
      console.log('执行用户脚本，内容长度:', scriptContent ? scriptContent.length : 0);
      
      if (!scriptContent) {
        console.warn('⚠️ 没有脚本内容可执行');
        return false;
      }
      
      try {
        // 在独立的作用域中执行脚本
        (function() {
          eval(scriptContent);
        })();
        
        // 延迟执行初始化
        setTimeout(function() {
          if (typeof window.main === 'function') {
            console.log('执行main函数...');
            window.main();
          }
          
          // 再次延迟检查UI是否显示
          setTimeout(function() {
            if (!document.getElementById('floating-icon')) {
              console.log('未检测到浮动图标，强制初始化...');
              window.xiaomeihuaForceInit();
            }
            
            // 强制显示浮动图标
            window.forceShowFloatingIcon();
            
            // 设置定时器，每隔一段时间检查并强制显示
            setInterval(window.forceShowFloatingIcon, 5000);
          }, 3000);
        }, 1000);
        
        console.log('✅ 脚本执行完成');
        return true;
      } catch (error) {
        console.error('❌ 脚本执行失败:', error);
        return false;
      }
    };
    
    // 触发自定义事件，通知API环境已准备就绪
    document.addEventListener('DOMContentLoaded', function() {
      const event = new CustomEvent('xiaomeihuaApiReady', {
        detail: {
          licenseKey: "${licenseKey}",
          shopInfo: window.xiaomeihuaShopInfo,
          apiVersion: '2.0.0'
        }
      });
      window.dispatchEvent(event);
      console.log('🚀 小梅花API环境已准备就绪，等待URL匹配检查');
    });
  `;
}

module.exports = {
  getKamiyanScript,
  getXinkamiScript,
  getTampermonkeyScript,
  createInjectionScript,
  matchUrl
};